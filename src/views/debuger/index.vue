<template>
  <div class="statistic-demo-page">
    <div class="page-header">
      <h1>StatisticCard 统计卡片组件演示</h1>
      <p>参照现代化设计的美观统计卡片组件，支持数字动画、自定义图标和响应式布局</p>
    </div>

    <!-- 基础展示 -->
    <div class="demo-section">
      <h2>基础展示</h2>
      <StatisticCard :schema="basicSchema" :data="statisticData" :column="4" :gap="20" />
    </div>

    <!-- 不同主题色 -->
    <div class="demo-section">
      <h2>不同主题色</h2>
      <StatisticCard :schema="colorfulSchema" :data="statisticData" :column="3" :gap="24" />
    </div>

    <!-- 自定义渲染 -->
    <div class="demo-section">
      <h2>自定义渲染</h2>
      <StatisticCard :schema="customRenderSchema" :data="statisticData" :column="2" :gap="24" />
    </div>

    <!-- 控制面板 -->
    <div class="demo-section">
      <h2>动态控制</h2>
      <div class="control-panel">
        <button @click="refreshData" class="control-btn primary">
          <Icon icon="ant-design:reload-outlined" />
          刷新数据
        </button>
        <button @click="toggleAnimation" class="control-btn">
          <Icon icon="ant-design:play-circle-outlined" />
          {{ animationEnabled ? '关闭' : '开启' }}动画
        </button>
        <button @click="changeLayout" class="control-btn">
          <Icon icon="ant-design:layout-outlined" />
          切换布局 ({{ currentColumns }}列)
        </button>
      </div>
      <StatisticCard @register="register" />
    </div>

    <!-- 响应式展示 -->
    <div class="demo-section">
      <h2>响应式布局</h2>
      <StatisticCard
        :schema="responsiveSchema"
        :data="statisticData"
        :column="{ xxl: 6, xl: 4, lg: 3, md: 2, sm: 2, xs: 1 }"
        :gap="16"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { StatisticCard, useStatisticCard } from '@/components/Custom';
  import type { StatisticItem } from '@/components/Custom/StatisticCard';
  import Icon from '@/components/Icon/Icon.vue';

  // 统计数据
  const statisticData = reactive({
    visits: 2000,
    sales: 20000,
    downloads: 8000,
    revenue: 5000,
    users: 120000,
    orders: 500000,
    conversion: 120000,
    satisfaction: 50000,
    growth: 15.8,
    rate: 3.2,
  });

  // 基础展示配置 - 参考 GrowCard 布局
  const basicSchema: StatisticItem[] = [
    {
      field: 'visits',
      title: '访问数',
      value: 'visits',
      icon: 'ant-design:eye-outlined',
      theme: 'green',
      valueColor: '#52c41a',
      prefix: '$',
      unit: '月',
      footer: {
        title: '总访问数',
        value: 'users',
        prefix: '$',
      },
    },
    {
      field: 'sales',
      title: '成交额',
      value: 'sales',
      icon: 'ant-design:credit-card-outlined',
      theme: 'blue',
      valueColor: '#1890ff',
      prefix: '$',
      unit: '月',
      footer: {
        title: '总成交额',
        value: 'orders',
        prefix: '$',
      },
    },
    {
      field: 'downloads',
      title: '下载数',
      value: 'downloads',
      icon: 'ant-design:download-outlined',
      theme: 'orange',
      valueColor: '#faad14',
      prefix: '$',
      unit: '周',
      footer: {
        title: '总下载数',
        value: 'conversion',
        prefix: '$',
      },
    },
    {
      field: 'revenue',
      title: '成交数',
      value: 'revenue',
      icon: 'ant-design:pie-chart-outlined',
      theme: 'purple',
      valueColor: '#722ed1',
      prefix: '$',
      unit: '年',
      footer: {
        title: '总成交数',
        value: 'satisfaction',
        prefix: '$',
      },
    },
  ];

  // 彩色主题配置 - 带单位标签
  const colorfulSchema: StatisticItem[] = [
    {
      field: 'users',
      title: '用户增长',
      value: 'users',
      icon: 'ant-design:user-outlined',
      theme: 'purple',
      valueColor: '#722ed1',
      duration: 2000,
      unit: '日',
      footer: {
        title: '累计用户',
        value: 'visits',
        render: (val: number) => `${(val * 60).toLocaleString()}`,
      },
    },
    {
      field: 'orders',
      title: '订单量',
      value: 'orders',
      icon: 'ant-design:shopping-cart-outlined',
      theme: 'cyan',
      valueColor: '#13c2c2',
      duration: 2500,
      unit: '月',
      footer: {
        title: '累计订单',
        value: 'sales',
        render: (val: number) => `${(val * 25).toLocaleString()}`,
      },
    },
    {
      field: 'conversion',
      title: '转化率',
      value: 'conversion',
      icon: 'ant-design:rise-outlined',
      theme: 'red',
      valueColor: '#f5222d',
      duration: 1800,
      unit: '周',
      suffix: '%',
      decimals: 1,
      render: (val: number) => `${(val / 10000).toFixed(1)}`,
      footer: {
        title: '平均转化',
        value: 'rate',
        suffix: '%',
        decimals: 1,
      },
    },
  ];

  // 自定义渲染配置 - 高级功能展示
  const customRenderSchema: StatisticItem[] = [
    {
      field: 'satisfaction',
      title: '客户满意度',
      value: 'satisfaction',
      icon: 'ant-design:smile-outlined',
      theme: 'green',
      valueColor: '#52c41a',
      render: (val: number) => `${(val / 10000).toFixed(1)}万`,
      unit: '评分',
      footer: {
        title: '好评率',
        value: 'rate',
        suffix: '%',
        decimals: 1,
        color: '#52c41a',
        render: (val: number) => `${(val * 30).toFixed(1)}`,
      },
    },
    {
      field: 'growth',
      title: '业务增长',
      value: 'growth',
      icon: 'ant-design:arrow-up-outlined',
      theme: 'blue',
      valueColor: '#1890ff',
      suffix: '%',
      decimals: 1,
      unit: '同比',
      footer: {
        title: '环比增长',
        value: 'rate',
        suffix: '%',
        decimals: 1,
        color: '#1890ff',
        render: (val: number) => `${(val * 5).toFixed(1)}`,
      },
    },
  ];

  // 响应式配置 - 简洁版本
  const responsiveSchema: StatisticItem[] = [
    {
      field: 'visits',
      title: '访问',
      value: 'visits',
      icon: 'ant-design:eye-outlined',
      theme: 'blue',
      valueColor: '#1890ff',
      unit: '日',
    },
    {
      field: 'sales',
      title: '成交',
      value: 'sales',
      icon: 'ant-design:credit-card-outlined',
      theme: 'green',
      valueColor: '#52c41a',
      unit: '月',
    },
    {
      field: 'downloads',
      title: '下载',
      value: 'downloads',
      icon: 'ant-design:download-outlined',
      theme: 'orange',
      valueColor: '#faad14',
      unit: '周',
    },
    {
      field: 'revenue',
      title: '收入',
      value: 'revenue',
      icon: 'ant-design:dollar-outlined',
      theme: 'red',
      valueColor: '#f5222d',
      unit: '年',
    },
    {
      field: 'users',
      title: '用户',
      value: 'users',
      icon: 'ant-design:team-outlined',
      theme: 'purple',
      valueColor: '#722ed1',
      unit: '总',
    },
    {
      field: 'orders',
      title: '订单',
      value: 'orders',
      icon: 'ant-design:shopping-outlined',
      theme: 'cyan',
      valueColor: '#13c2c2',
      unit: '笔',
    },
  ];
  const animationEnabled = ref(true);
  const currentColumns = ref(4);
  // Hook 控制
  const [register, { setStatisticProps }] = useStatisticCard({
    schema: basicSchema,
    data: statisticData,
    column: currentColumns.value,
  });

  // 控制方法
  const refreshData = () => {
    Object.assign(statisticData, {
      visits: Math.floor(Math.random() * 5000) + 1000,
      sales: Math.floor(Math.random() * 50000) + 10000,
      downloads: Math.floor(Math.random() * 20000) + 5000,
      revenue: Math.floor(Math.random() * 10000) + 2000,
      users: Math.floor(Math.random() * 200000) + 50000,
      orders: Math.floor(Math.random() * 800000) + 200000,
      conversion: Math.floor(Math.random() * 200000) + 50000,
      satisfaction: Math.floor(Math.random() * 80000) + 20000,
      growth: Math.random() * 30 + 5,
      rate: Math.random() * 10 + 1,
    });

    setStatisticProps({
      data: statisticData,
    });
  };

  const toggleAnimation = () => {
    animationEnabled.value = !animationEnabled.value;

    const updatedSchema = basicSchema.map((item) => ({
      ...item,
      useAnimation: animationEnabled.value,
    }));

    setStatisticProps({
      schema: updatedSchema,
    });
  };

  const changeLayout = () => {
    const layouts = [2, 3, 4, 5];
    const currentIndex = layouts.indexOf(currentColumns.value);
    const nextIndex = (currentIndex + 1) % layouts.length;
    currentColumns.value = layouts[nextIndex];

    setStatisticProps({
      column: currentColumns.value,
    });
  };
</script>

<style scoped>
  .statistic-demo-page {
    min-height: 100vh;
    padding: 24px;
    background: #f5f5f5;
  }

  .page-header {
    margin-bottom: 48px;
    padding: 32px;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
  }

  .page-header h1 {
    margin: 0 0 12px;
    font-size: 32px;
    font-weight: 700;
  }

  .page-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
  }

  .demo-section {
    margin-bottom: 48px;
    padding: 32px;
    border-radius: 16px;
    background: white;
    box-shadow: 0 4px 20px rgb(0 0 0 / 8%);
  }

  .demo-section h2 {
    margin: 0 0 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e5e7eb;
    color: #1f2937;
    font-size: 24px;
    font-weight: 600;
  }

  .control-panel {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 24px;
    gap: 16px;
  }

  .control-btn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    transition: all 0.2s ease;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    gap: 8px;
  }

  .control-btn:hover {
    transform: translateY(-1px);
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgb(59 130 246 / 15%);
    color: #3b82f6;
  }

  .control-btn.primary {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
  }

  .control-btn.primary:hover {
    border-color: #2563eb;
    background: #2563eb;
    color: white;
  }

  @media (max-width: 768px) {
    .statistic-demo-page {
      padding: 16px;
    }

    .page-header {
      margin-bottom: 32px;
      padding: 24px;
    }

    .page-header h1 {
      font-size: 24px;
    }

    .demo-section {
      margin-bottom: 32px;
      padding: 24px;
    }

    .demo-section h2 {
      font-size: 20px;
    }

    .control-panel {
      flex-direction: column;
    }

    .control-btn {
      justify-content: center;
    }
  }
</style>
