<template>
  <div :class="[prefixCls]" :style="containerStyle">
    <div :class="`${prefixCls}__grid`" :style="gridStyle">
      <template v-for="(item, index) in visibleItems" :key="item.field">
        <div
          :class="[`${prefixCls}__card`, item.className, getCardClasses(index)]"
          :style="[getCardStyle(item), item.style]"
          @click="handleItemClick(item)"
        >
          <!-- 加载状态 -->
          <div v-if="loading" :class="`${prefixCls}__loading`">
            <div :class="`${prefixCls}__loading-spinner`"></div>
          </div>

          <!-- 卡片头部 -->
          <div :class="`${prefixCls}__header`">
            <div :class="`${prefixCls}__title`">{{ getCardTitle(item) }}</div>
            <!-- 单位标签 -->
            <div v-if="item.unit" :class="`${prefixCls}__unit`">
              <Tag :color="getTagColor(item)">{{ item.unit }}</Tag>
            </div>
          </div>

          <!-- 主要内容：数值和图标 -->
          <div :class="`${prefixCls}__main`">
            <!-- 数值 -->
            <div :class="`${prefixCls}__value`" :style="{ color: item.valueColor }">
              <template v-if="item.render">
                <span :class="`${prefixCls}__count`">{{ renderContent(item) }}</span>
              </template>
              <template v-else-if="item.useAnimation !== false">
                <CountTo
                  :start-val="1"
                  :end-val="getNumericValue(item)"
                  :duration="item.duration || 1500"
                  :decimals="item.decimals || 0"
                  :separator="item.separator || ','"
                  :decimal="item.decimal || '.'"
                  :prefix="item.prefix || ''"
                  :suffix="item.suffix || ''"
                  :color="item.valueColor"
                  :class="`${prefixCls}__count`"
                />
              </template>
              <template v-else>
                <span :class="`${prefixCls}__count`">{{ formatStaticValue(item) }}</span>
              </template>
            </div>

            <!-- 图标 -->
            <div v-if="item.icon" :class="`${prefixCls}__icon`" :style="getIconStyle(item)">
              <Icon
                v-if="typeof item.icon === 'string' && item.icon"
                :icon="item.icon"
                :size="40"
              />
              <component v-else-if="item.icon" :is="item.icon" />
            </div>
          </div>

          <!-- 底部统计 -->
          <div v-if="item.footer" :class="`${prefixCls}__footer`">
            <span :class="`${prefixCls}__footer-title`">{{
              item.footer.title || `总${item.title}`
            }}</span>
            <span :class="`${prefixCls}__footer-value`">
              <template v-if="item.footer.render">
                {{ renderFooterContent(item) }}
              </template>
              <template v-else-if="item.useAnimation !== false">
                <CountTo
                  :start-val="1"
                  :end-val="getFooterValue(item)"
                  :duration="(item.duration || 1500) + 200"
                  :decimals="item.footer.decimals || 0"
                  :separator="item.footer.separator || ','"
                  :prefix="item.footer.prefix || ''"
                  :suffix="item.footer.suffix || ''"
                />
              </template>
              <template v-else>
                {{ formatFooterValue(item) }}
              </template>
            </span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="tsx">
  import {
    defineComponent,
    computed,
    ref,
    unref,
    onMounted,
    onUnmounted,
    type PropType,
    type CSSProperties,
  } from 'vue';
  import { useDesign } from '@/hooks/web/useDesign';
  import Icon from '@/components/Icon/Icon.vue';
  import { CountTo } from '@/components/CountTo';
  import { Tag } from 'ant-design-vue';
  import { isFunction, isNumber } from '@/utils/is';
  import { get } from 'lodash-es';
  import type { StatisticItem, StatisticCardProps, StatisticCardInstance } from './typing';

  const props = {
    schema: {
      type: Array as PropType<StatisticItem[]>,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    column: {
      type: [Number, Object],
      default: () => ({ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }),
    },
    gap: {
      type: [Number, String],
      default: 16,
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    borderRadius: {
      type: [Number, String],
      default: 4,
    },
    shadow: {
      type: String,
      default: '0 2px 8px rgba(0, 0, 0, 0.1)',
    },
    backgroundColor: {
      type: String,
      default: '#ffffff',
    },
    padding: {
      type: [Number, String],
      default: 20,
    },
    minHeight: {
      type: [Number, String],
      default: 120,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  };

  export default defineComponent({
    name: 'StatisticCard',
    components: { Icon, CountTo, Tag },
    props,
    emits: ['register'],
    setup(props, { emit }) {
      const { prefixCls } = useDesign('statistic-card');
      const propsRef = ref<Partial<StatisticCardProps> | null>(null);

      const getMergeProps = computed(() => {
        return {
          ...props,
          ...(unref(propsRef) as any),
        } as StatisticCardProps;
      });

      const visibleItems = computed(() => {
        const { schema, data } = unref(getMergeProps);
        return schema.filter((item) => {
          if (item.show && isFunction(item.show)) {
            return item.show(data);
          }
          return true;
        });
      });

      const containerStyle = computed((): CSSProperties => {
        return {
          padding: `${props.padding}px`,
        };
      });

      // 获取当前屏幕宽度对应的列数
      const getCurrentColumns = (column: any): number => {
        if (isNumber(column)) {
          return column;
        }

        if (typeof column === 'object') {
          // 模拟媒体查询逻辑
          const width = window.innerWidth;

          if (width >= 1600 && column.xxl) return column.xxl;
          if (width >= 1200 && column.xl) return column.xl;
          if (width >= 992 && column.lg) return column.lg;
          if (width >= 768 && column.md) return column.md;
          if (width >= 576 && column.sm) return column.sm;
          if (column.xs) return column.xs;

          // 默认值
          return 1;
        }

        return 4; // 默认4列
      };

      // 响应式窗口宽度
      const windowWidth = ref(window.innerWidth);

      const updateWindowWidth = () => {
        windowWidth.value = window.innerWidth;
      };

      onMounted(() => {
        window.addEventListener('resize', updateWindowWidth);
      });

      onUnmounted(() => {
        window.removeEventListener('resize', updateWindowWidth);
      });

      const gridStyle = computed((): CSSProperties => {
        const { gap, column } = unref(getMergeProps);
        // 触发响应式更新
        const width = windowWidth.value;
        const cols = getCurrentColumns(column);

        console.log('Grid style - window width:', width, 'columns:', cols, 'config:', column);

        return {
          display: 'grid',
          gridTemplateColumns: `repeat(${cols}, 1fr)`,
          gap: `${gap}px`,
        };
      });

      function getNumericValue(item: StatisticItem): number {
        const { data } = unref(getMergeProps);
        let value = item.value;

        if (typeof value === 'string' && data) {
          value = get(data, item.field, value);
        }

        // 确保返回有效的数字
        if (isNumber(value)) {
          return value;
        }

        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? 0 : parsed;
      }

      function formatStaticValue(item: StatisticItem): string {
        const value = getNumericValue(item);
        const { prefix = '', suffix = '', separator = ',', decimal = '.', decimals = 0 } = item;

        let formattedValue = value.toFixed(decimals);
        const parts = formattedValue.split('.');
        let integerPart = parts[0];
        const decimalPart = parts.length > 1 ? decimal + parts[1] : '';

        // 添加千分位分隔符
        const rgx = /(\d+)(\d{3})/;
        while (rgx.test(integerPart)) {
          integerPart = integerPart.replace(rgx, '$1' + separator + '$2');
        }

        return prefix + integerPart + decimalPart + suffix;
      }

      function renderContent(item: StatisticItem): string {
        const { data } = unref(getMergeProps);
        const value = getNumericValue(item);

        if (item.render && isFunction(item.render)) {
          const result = item.render(value, data || {});
          // 确保返回字符串
          return String(result);
        }

        return formatStaticValue(item);
      }

      function handleItemClick(item: StatisticItem) {
        const { data } = unref(getMergeProps);
        if (item.onClick && isFunction(item.onClick)) {
          item.onClick(item, data || {});
        }
      }

      function getIconBackground(color?: string): string {
        if (!color) {
          return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }

        // 根据颜色生成渐变背景
        const colorMap: Record<string, string> = {
          '#1890ff': 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
          '#52c41a': 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
          '#faad14': 'linear-gradient(135deg, #faad14 0%, #d46b08 100%)',
          '#f5222d': 'linear-gradient(135deg, #f5222d 0%, #cf1322 100%)',
          '#722ed1': 'linear-gradient(135deg, #722ed1 0%, #531dab 100%)',
          '#13c2c2': 'linear-gradient(135deg, #13c2c2 0%, #08979c 100%)',
        };

        return colorMap[color] || `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`;
      }

      function getIconStyle(item: StatisticItem): CSSProperties {
        const themeColor = getThemeColor(item);

        // 根据主题色生成玻璃效果的渐变
        const gradientMap: Record<string, string> = {
          '#1890ff':
            'linear-gradient(135deg, rgba(24, 144, 255, 0.8) 0%, rgba(9, 109, 217, 0.8) 100%)',
          '#52c41a':
            'linear-gradient(135deg, rgba(82, 196, 26, 0.8) 0%, rgba(56, 158, 13, 0.8) 100%)',
          '#faad14':
            'linear-gradient(135deg, rgba(250, 173, 20, 0.8) 0%, rgba(212, 107, 8, 0.8) 100%)',
          '#f5222d':
            'linear-gradient(135deg, rgba(245, 34, 45, 0.8) 0%, rgba(207, 19, 34, 0.8) 100%)',
          '#722ed1':
            'linear-gradient(135deg, rgba(114, 46, 209, 0.8) 0%, rgba(83, 29, 171, 0.8) 100%)',
          '#13c2c2':
            'linear-gradient(135deg, rgba(19, 194, 194, 0.8) 0%, rgba(8, 151, 156, 0.8) 100%)',
        };

        // 悬停效果的更亮渐变
        const hoverGradientMap: Record<string, string> = {
          '#1890ff':
            'linear-gradient(135deg, rgba(24, 144, 255, 0.9) 0%, rgba(9, 109, 217, 0.9) 100%)',
          '#52c41a':
            'linear-gradient(135deg, rgba(82, 196, 26, 0.9) 0%, rgba(56, 158, 13, 0.9) 100%)',
          '#faad14':
            'linear-gradient(135deg, rgba(250, 173, 20, 0.9) 0%, rgba(212, 107, 8, 0.9) 100%)',
          '#f5222d':
            'linear-gradient(135deg, rgba(245, 34, 45, 0.9) 0%, rgba(207, 19, 34, 0.9) 100%)',
          '#722ed1':
            'linear-gradient(135deg, rgba(114, 46, 209, 0.9) 0%, rgba(83, 29, 171, 0.9) 100%)',
          '#13c2c2':
            'linear-gradient(135deg, rgba(19, 194, 194, 0.9) 0%, rgba(8, 151, 156, 0.9) 100%)',
        };

        const gradient =
          gradientMap[themeColor] ||
          `linear-gradient(135deg, ${themeColor}cc 0%, ${themeColor}99 100%)`;

        const hoverGradient =
          hoverGradientMap[themeColor] ||
          `linear-gradient(135deg, ${themeColor}dd 0%, ${themeColor}aa 100%)`;

        return {
          '--icon-gradient': gradient,
          '--icon-hover-gradient': hoverGradient,
        } as CSSProperties;
      }

      function getThemeColor(item: StatisticItem): string {
        if (item.iconColor) {
          return item.iconColor;
        }

        const themeColorMap: Record<string, string> = {
          blue: '#1890ff',
          green: '#52c41a',
          orange: '#faad14',
          red: '#f5222d',
          purple: '#722ed1',
          cyan: '#13c2c2',
        };

        return themeColorMap[item.theme || 'blue'] || item.theme || '#1890ff';
      }

      function getThemeClass(item: StatisticItem): string {
        return item.theme || 'blue';
      }

      function getFooterValue(item: StatisticItem): number {
        const { data } = unref(getMergeProps);
        let value = item.footer?.value;

        if (typeof value === 'string' && data) {
          value = get(data, item.footer?.field || value, value);
        }

        if (isNumber(value)) {
          return value;
        }

        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? 0 : parsed;
      }

      function formatFooterValue(item: StatisticItem): string {
        const value = getFooterValue(item);
        const { prefix = '', suffix = '', separator = ',', decimal = '.' } = item.footer || {};

        let formattedValue = value.toFixed(0);
        const parts = formattedValue.split('.');
        let integerPart = parts[0];
        const decimalPart = parts.length > 1 ? decimal + parts[1] : '';

        // 添加千分位分隔符
        const rgx = /(\d+)(\d{3})/;
        while (rgx.test(integerPart)) {
          integerPart = integerPart.replace(rgx, '$1' + separator + '$2');
        }

        return prefix + integerPart + decimalPart + suffix;
      }

      function renderFooterContent(item: StatisticItem): string {
        const { data } = unref(getMergeProps);
        const value = getFooterValue(item);

        if (item.footer?.render && isFunction(item.footer.render)) {
          const result = item.footer.render(value, data || {});
          return String(result);
        }

        return formatFooterValue(item);
      }

      function getCardTitle(item: StatisticItem): string {
        if (typeof item.title === 'string') {
          return item.title;
        }
        return String(item.title);
      }

      function getCardClasses(index: number): string {
        const classes: string[] = [`${prefixCls}__card-item`];

        // 添加索引类用于间距控制
        if (index > 0) {
          classes.push(`${prefixCls}__card-item--mt`);
        }

        return classes.join(' ');
      }

      function getCardStyle(item: StatisticItem): CSSProperties {
        const { bordered, borderRadius, shadow, backgroundColor, minHeight } = unref(getMergeProps);

        return {
          backgroundColor,
          borderRadius: `${borderRadius}px`,
          boxShadow: shadow,
          border: bordered ? '1px solid #f0f0f0' : 'none',
          minHeight: `${minHeight}px`,
          position: 'relative',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          overflow: 'hidden',
          ...item.style,
        };
      }

      function getTagColor(item: StatisticItem): string {
        const themeColorMap: Record<string, string> = {
          blue: 'blue',
          green: 'green',
          orange: 'orange',
          red: 'red',
          purple: 'purple',
          cyan: 'cyan',
        };

        return themeColorMap[item.theme || 'blue'] || 'blue';
      }

      function setStatisticProps(statisticProps: Partial<StatisticCardProps>): void {
        propsRef.value = {
          ...(unref(propsRef) as Record<string, any>),
          ...statisticProps,
        } as Record<string, any>;
      }

      const methods: StatisticCardInstance = {
        setStatisticProps,
      };

      emit('register', methods);

      return {
        prefixCls,
        visibleItems,
        containerStyle,
        gridStyle,
        getNumericValue,
        formatStaticValue,
        renderContent,
        handleItemClick,
        getIconBackground,
        getIconStyle,
        getThemeColor,
        getThemeClass,
        getFooterValue,
        formatFooterValue,
        renderFooterContent,
        getCardTitle,
        getCardClasses,
        getCardStyle,
        getTagColor,
      };
    },
  });
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-statistic-card';

  .@{prefix-cls} {
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    &__grid {
      // Grid 布局由 JavaScript 控制
      width: 100%;
    }

    &__card {
      display: flex;
      flex-direction: column;
      // 基础卡片样式
      min-width: 0;
      padding: 20px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgb(0 0 0 / 15%) !important;
      }
    }

    &__loading {
      display: flex;
      position: absolute;
      z-index: 1;
      inset: 0;
      align-items: center;
      justify-content: center;
      background: rgb(255 255 255 / 80%);
    }

    &__loading-spinner {
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #1890ff;
      border-radius: 50%;
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    &__title {
      color: #8c8c8c;
      font-size: 14px;
      font-weight: 500;
    }

    &__unit {
      flex-shrink: 0;
    }

    &__main {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
    }

    &__value {
      flex: 1;
    }

    &__count {
      color: #262626;
      font-size: 24px;
      font-weight: 600;
      line-height: 32px;
    }

    &__icon {
      display: flex;
      position: relative;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      margin-left: 16px;
      overflow: hidden;
      border: 1px solid rgb(255 255 255 / 20%);
      border-radius: 6px;

      // 玻璃效果背景
      background: rgb(255 255 255 / 8%);
      box-shadow:
        0 4px 16px rgb(0 0 0 / 8%),
        inset 0 1px 0 rgb(255 255 255 / 15%);
      backdrop-filter: blur(8px);

      // 渐变叠加层
      &::before {
        content: '';
        position: absolute;
        z-index: 1;
        inset: 0;
        background: var(
          --icon-gradient,
          linear-gradient(135deg, rgb(103 126 234 / 80%) 0%, rgb(118 75 162 / 80%) 100%)
        );
      }

      // 高光效果
      &::after {
        content: '';
        position: absolute;
        z-index: 2;
        top: 1px;
        right: 1px;
        left: 1px;
        height: 50%;
        border-radius: 5px 5px 0 0;
        background: linear-gradient(
          180deg,
          rgb(255 255 255 / 25%) 0%,
          rgb(255 255 255 / 8%) 50%,
          transparent 100%
        );
      }

      // 图标层级
      :deep(.anticon),
      :deep(svg) {
        position: relative;
        z-index: 3;
        color: white;
        filter: drop-shadow(0 1px 2px rgb(0 0 0 / 30%));
      }

      // 悬停效果
      &:hover {
        transform: translateY(-1px);
        box-shadow:
          0 8px 24px rgb(0 0 0 / 12%),
          inset 0 1px 0 rgb(255 255 255 / 25%);

        &::before {
          // 悬停时使用更亮的渐变
          background: var(
            --icon-hover-gradient,
            linear-gradient(135deg, rgb(103 126 234 / 90%) 0%, rgb(118 75 162 / 90%) 100%)
          );
        }
      }
    }

    &__footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }

    &__footer-title {
      color: #8c8c8c;
      font-size: 12px;
      font-weight: 400;
    }

    &__footer-value {
      color: #262626;
      font-size: 14px;
      font-weight: 600;
    }
  }
</style>
