<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">StatisticCard 测试页面</h1>
    
    <!-- 测试基础功能 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">基础功能测试 (4px 边框弧度)</h2>
      <StatisticCard :schema="testSchema" :data="testData" :column="4" :gap="16" />
    </div>

    <!-- 测试加载状态 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">加载状态测试</h2>
      <StatisticCard :schema="testSchema" :data="testData" :column="4" :gap="16" :loading="true" />
    </div>

    <!-- 测试自定义样式 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">自定义样式测试</h2>
      <StatisticCard 
        :schema="customSchema" 
        :data="testData" 
        :column="3" 
        :gap="20"
        border-radius="8"
        shadow="0 4px 16px rgba(0, 0, 0, 0.15)"
        background-color="#f8fafc"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { StatisticCard } from '@/components/Custom';
import type { StatisticItem } from '@/components/Custom/StatisticCard';

// 测试数据
const testData = reactive({
  visits: 2000,
  sales: 20000,
  downloads: 8000,
  revenue: 5000,
});

// 基础测试配置
const testSchema: StatisticItem[] = [
  {
    field: 'visits',
    title: '访问数',
    value: 'visits',
    icon: 'ant-design:eye-outlined',
    theme: 'green',
    valueColor: '#52c41a',
    unit: '月',
    footer: {
      title: '总访问数',
      value: 12000,
    },
  },
  {
    field: 'sales',
    title: '成交额',
    value: 'sales',
    icon: 'ant-design:credit-card-outlined',
    theme: 'blue',
    valueColor: '#1890ff',
    prefix: '$',
    unit: '月',
    footer: {
      title: '总成交额',
      value: 500000,
      prefix: '$',
    },
  },
  {
    field: 'downloads',
    title: '下载数',
    value: 'downloads',
    icon: 'ant-design:download-outlined',
    theme: 'orange',
    valueColor: '#faad14',
    unit: '周',
  },
  {
    field: 'revenue',
    title: '收入',
    value: 'revenue',
    icon: 'ant-design:dollar-outlined',
    theme: 'red',
    valueColor: '#f5222d',
    prefix: '$',
    suffix: '.00',
    unit: '日',
  },
];

// 自定义样式配置
const customSchema: StatisticItem[] = [
  {
    field: 'visits',
    title: '用户访问',
    value: 'visits',
    icon: 'ant-design:user-outlined',
    theme: 'purple',
    valueColor: '#722ed1',
    render: (val: number) => `${(val / 1000).toFixed(1)}K`,
    unit: '人次',
  },
  {
    field: 'sales',
    title: '销售业绩',
    value: 'sales',
    icon: 'ant-design:trophy-outlined',
    theme: 'cyan',
    valueColor: '#13c2c2',
    prefix: '¥',
    decimals: 2,
    separator: ',',
    unit: '元',
  },
  {
    field: 'downloads',
    title: '下载量',
    value: 'downloads',
    icon: 'ant-design:cloud-download-outlined',
    theme: 'green',
    valueColor: '#52c41a',
    suffix: '+',
    unit: '次',
    footer: {
      title: '本月新增',
      value: 1200,
      suffix: '+',
    },
  },
];
</script>
